import { requestClient } from '#/api/request';

/**
 * AI混剪视频相关接口
 */

/**
 * 视频模版项接口
 */
export interface VideoTemplate {
  id: number;
  title: string;
  captions: string;
  img_url: string;
  url: string;
  create_time: null | string;
  update_time: null | string;
  delete_time: null | string;
}

/**
 * 视频背景项接口
 */
export interface VideoBackground {
  id: number | string;
  url: string;
  oss_url: string;
  create_time: null | string;
  update_time: null | string;
  delete_time: null | string;
}

/**
 * 音乐分类接口
 */
export interface MusicCategory {
  id: number;
  id_str: string;
  name: string;
  aweme_cover: any;
  cover: any;
  is_hot: boolean;
  level: number;
}

/**
 * 音乐项接口
 */
export interface MusicItem {
  id: number;
  id_str: string;
  title: string;
  author: string;
  duration: number;
  play_url: {
    height: number;
    uri: string;
    url_key: string;
    url_list: string[];
    width: number;
  };
  album: string;
  cover_hd: any;
  cover_large: any;
  cover_medium: any;
  cover_thumb: any;
}

/**
 * 剪辑任务参数接口
 */
export interface ClipTaskParams {
  title: string; // 主标题，多个用逗号分隔
  video_id: string; // 作品ID，多个用逗号分隔
  model_id: string; // 模板ID，多个用逗号分隔
  multiple: number | string; // 批量剪辑倍数
  background_music: string; // 背景音乐URL，多个用逗号分隔
  material_url: string; // 素材URL
  background_img_id: number | string; // 背景图片ID
  title_show_time: number; // 标题显示时间
}

/**
 * 获取视频模版列表
 * @returns Promise<{list: VideoTemplate[], total: number}>
 */
export function getVideoTemplateList(): Promise<{
  list: VideoTemplate[];
  total: number;
}> {
  return requestClient.post('/mobile/Clip/modelList');
}

/**
 * 获取视频背景列表
 * @returns Promise<{list: VideoBackground[], total: number}>
 */
export function getVideoBackgroundList(): Promise<{
  list: VideoBackground[];
  total: number;
}> {
  return requestClient.post('/mobile/Clip/backgroundImgList');
}

/**
 * 获取音乐分类列表
 * @returns Promise<{data: MusicCategory[]}>
 */
export function getMusicCategories(): Promise<{ data: MusicCategory[] }> {
  return requestClient.get('/mobile/Clip/musicCollects');
}

/**
 * 获取音乐列表
 * @param categoryId 分类ID
 * @returns Promise<{data: MusicItem[]}>
 */
export function getMusicList(
  categoryId: string,
): Promise<{ data: MusicItem[] }> {
  return requestClient.get('/mobile/Clip/musicList', {
    params: { mcid: categoryId },
  });
}

/**
 * 创建剪辑任务
 * @param params 剪辑任务参数
 * @returns Promise<{errno: number, message: string}>
 */
export function createClipTask(
  params: ClipTaskParams,
): Promise<{ errno: number; message: string }> {
  return requestClient.post('/mobile/Clip/clipTask', params);
}
